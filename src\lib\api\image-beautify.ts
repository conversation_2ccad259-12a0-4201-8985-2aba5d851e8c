/**
 * 图片美化 API
 */

import { apiClient } from './client'
import type { 
  ProcessingOptions, 
  ProcessingProgress, 
  UploadResponse, 
  ProcessingResponse,
  ApiResponse 
} from './types'

/**
 * 上传图片
 */
export async function uploadImage(file: File): Promise<string> {
  const formData = new FormData()
  formData.append('image', file)

  const response = await apiClient.upload<UploadResponse>('/image-processing/upload', formData)
  
  if (!response.success || !response.data) {
    throw new Error(response.error?.message || 'Upload failed')
  }

  return response.data.imageUrl
}

/**
 * 处理图片
 */
export async function processImage(
  imageUrl: string,
  options: ProcessingOptions,
  onProgress?: (progress: ProcessingProgress) => void
): Promise<ProcessingResponse> {
  // 开始处理
  onProgress?.({
    stage: 'analyzing',
    progress: 0,
    message: 'Starting image processing...'
  })

  const response = await apiClient.post<ProcessingResponse>('/image-processing/process', {
    imageUrl,
    type: options.type,
    intensity: options.intensity,
    quality: options.quality,
  })

  // 模拟进度更新
  onProgress?.({
    stage: 'processing',
    progress: 50,
    message: 'Processing image...'
  })

  if (!response.success || !response.data) {
    throw new Error(response.error?.message || 'Processing failed')
  }

  onProgress?.({
    stage: 'completed',
    progress: 100,
    message: 'Processing completed'
  })

  return response.data
}

/**
 * 下载处理后的图片
 */
export async function downloadProcessedImage(imageUrl: string): Promise<Blob> {
  const response = await fetch(imageUrl)
  
  if (!response.ok) {
    throw new Error(`Failed to download image: ${response.statusText}`)
  }

  return response.blob()
}

/**
 * 模拟 API 调用（开发阶段使用）
 */
export async function mockProcessImage(
  imageUrl: string,
  options: ProcessingOptions,
  onProgress?: (progress: ProcessingProgress) => void
): Promise<ApiResponse<ProcessingResponse>> {
  const stages: ProcessingProgress['stage'][] = ['analyzing', 'processing', 'optimizing', 'completed']
  
  for (let i = 0; i < stages.length; i++) {
    const progress = Math.round((i + 1) / stages.length * 100)
    onProgress?.({
      stage: stages[i],
      progress,
      message: `${stages[i]}...`
    })
    
    // 模拟处理时间
    await new Promise(resolve => setTimeout(resolve, 800))
  }

  // 返回原图片 URL（模拟处理结果）
  return {
    success: true,
    data: {
      processedImageUrl: imageUrl,
    }
  }
}

/**
 * 检查 API 健康状态
 */
export async function checkApiHealth(): Promise<boolean> {
  try {
    const response = await apiClient.get('/image-processing/health', {
      timeout: 5000,
      retries: 1,
    })
    return response.success
  } catch {
    return false
  }
}

/**
 * 重试包装器
 */
export async function withRetry<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: Error

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error as Error
      
      if (attempt === maxRetries) {
        break
      }

      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, attempt)))
    }
  }

  throw lastError!
}
