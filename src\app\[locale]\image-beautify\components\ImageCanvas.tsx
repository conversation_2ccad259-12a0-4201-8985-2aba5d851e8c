/**
 * 图片画布组件 - 分子组件
 */

'use client'

import { useEffect, useRef, useMemo } from 'react'
import { motion } from 'motion/react'
import { useTranslation } from '@/lib/i18n/client'
import { useImageCanvas } from '@/lib/hooks/useImageCanvas'
import type { FabricCanvas } from '@/types/image-beautify'

interface ImageCanvasPropsWithOffsets {
  imageUrl?: string
  onCanvasReady?: (canvas: FabricCanvas) => void
  className?: string
  topOffset?: number
  bottomOffset?: number
  enableZoom?: boolean
  enablePanning?: boolean
  enableReset?: boolean
  enableTouchpadGestures?: boolean
}

export default function ImageCanvas({
  imageUrl,
  onCanvasReady,
  className = '',
  topOffset = 0,
  bottomOffset = 0,
  enableZoom = false,
  enablePanning = false,
  enableReset = false,
  enableTouchpadGestures = false
}: ImageCanvasPropsWithOffsets) {
  const { t } = useTranslation()
  const containerRef = useRef<HTMLDivElement>(null)
  const canvasInstanceRef = useRef<FabricCanvas | null>(null)

  // 稳定的初始尺寸，避免每次渲染都变化
  const initialSize = useMemo(() => ({
    width: typeof window !== 'undefined' ? window.innerWidth : 1920,
    height: typeof window !== 'undefined' ? window.innerHeight : 1080,
  }), [])

  // 稳定的配置对象
  const canvasOptions = useMemo(() => ({
    width: initialSize.width,
    height: initialSize.height,
    backgroundColor: '#000000',
    enableZoom,
    enablePanning,
    enableReset,
    enableTouchpadGestures,
    topOffset,
    bottomOffset,
  }), [
    initialSize.width,
    initialSize.height,
    enableZoom,
    enablePanning,
    enableReset,
    enableTouchpadGestures,
    topOffset,
    bottomOffset,
  ])

  const {
    canvasRef,
    canvas,
    isReady,
    loadImage,
    updateSize,
  } = useImageCanvas(canvasOptions)

  // 更新 canvas 实例引用
  useEffect(() => {
    canvasInstanceRef.current = canvas
  }, [canvas])

  // 当画布准备就绪时通知父组件
  useEffect(() => {
    if (canvas && isReady && onCanvasReady) {
      onCanvasReady(canvas)
    }
  }, [canvas, isReady, onCanvasReady])

  // 当有新图片时加载到画布
  useEffect(() => {
    if (imageUrl && isReady) {
      loadImage(imageUrl, topOffset, bottomOffset).catch(error => {
        console.error('Failed to load image to canvas:', error)
      })
    }
  }, [imageUrl, isReady, loadImage, topOffset, bottomOffset])

  // 监听窗口尺寸变化，节流更新画布大小（仅更新尺寸与自适配，避免频繁重载图片）
  useEffect(() => {
    if (!isReady) return

    let raf = 0
    let last = 0
    const tick = () => {
      last = performance.now()
      updateSize(window.innerWidth, window.innerHeight)
    }
    const handleResize = () => {
      const now = performance.now()
      if (now - last < 60) {
        cancelAnimationFrame(raf)
        raf = requestAnimationFrame(tick)
        return
      }
      tick()
    }

    window.addEventListener('resize', handleResize)
    return () => {
      window.removeEventListener('resize', handleResize)
      cancelAnimationFrame(raf)
    }
  }, [isReady, updateSize])

  return (
    <div
      ref={containerRef}
      className={`fixed inset-0 bg-black ${className}`}
    >
      {/* 画布容器 */}
      <div className="absolute inset-0">
        <canvas
          ref={canvasRef}
          className="w-full h-full"
          style={{
            width: '100vw',
            height: '100vh',
            display: 'block'
          }}
        />

        {/* 加载状态 */}
        {!isReady && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/50">
            <div className="flex flex-col items-center space-y-4 text-white">
              <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin" />
            </div>
          </div>
        )}



        {/* 空状态提示 */}
        {isReady && !imageUrl && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="absolute inset-0 flex items-center justify-center"
          >
            <div className="text-center text-gray-400">
              <div className="w-16 h-16 mx-auto mb-4 border-2 border-dashed border-gray-600 rounded-lg flex items-center justify-center">
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <p className="text-lg font-medium mb-2">{t('imageEditor.upload.title', '上传图片')}</p>
              <p className="text-sm">{t('imageEditor.upload.description', '选择要处理的图片文件')}</p>
            </div>
          </motion.div>
        )}
      </div>

      {/* 画布边框 */}
      <div className="absolute inset-0 border border-gray-800 rounded-lg pointer-events-none" />
    </div>
  )
}
