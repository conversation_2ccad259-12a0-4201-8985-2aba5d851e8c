/**
 * 画布 Zustand Store
 */

import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import {
  loadImageToCanvas,
  updateCanvasSize,
  resetImagePosition,
  exportCanvasAsImage,
  exportCanvasAsBlob,
} from '@/lib/utils/canvas'
import type { FabricCanvas, FabricImage } from '@/types/image-beautify'

// 画布配置接口
export interface CanvasConfig {
  width: number
  height: number
  backgroundColor: string
  enableZoom: boolean
  enablePanning: boolean
  enableReset: boolean
  enableTouchpadGestures: boolean
  topOffset: number
  bottomOffset: number
}

// Store 状态接口
interface CanvasState {
  // 配置
  config: CanvasConfig
  
  // 状态
  isReady: boolean
  error: string | null
  currentImageUrl: string | null
  
  // 同步 Actions
  setConfig: (config: Partial<CanvasConfig>) => void
  setIsReady: (ready: boolean) => void
  setError: (error: string | null) => void
  setCurrentImageUrl: (url: string | null) => void
  updateSize: (width: number, height: number) => void
  reset: () => void
  
  // 异步 Actions（需要传入画布实例）
  loadImage: (canvas: FabricCanvas, imageUrl: string, topOffset?: number, bottomOffset?: number) => Promise<FabricImage>
  resetImage: (canvas: FabricCanvas) => void
  updateCanvasSize: (canvas: FabricCanvas, width: number, height: number) => void
  exportAsDataURL: (canvas: FabricCanvas, format?: 'png' | 'jpeg' | 'webp', quality?: number) => string | null
  exportAsBlob: (canvas: FabricCanvas, format?: 'png' | 'jpeg' | 'webp', quality?: number) => Promise<Blob | null>
}

// 默认配置
const defaultConfig: CanvasConfig = {
  width: 800,
  height: 600,
  backgroundColor: '#000000',
  enableZoom: false,
  enablePanning: false,
  enableReset: false,
  enableTouchpadGestures: false,
  topOffset: 0,
  bottomOffset: 0,
}

// 初始状态
const initialState = {
  config: defaultConfig,
  isReady: false,
  error: null,
  currentImageUrl: null,
}

// 创建 Store
export const useCanvasStore = create<CanvasState>()(
  devtools(
    (set, get) => ({
      // 初始状态
      ...initialState,
      
      // 同步 Actions
      setConfig: (newConfig) => set((state) => {
        // 检查配置是否真的有变化，避免不必要的更新
        const currentConfig = state.config
        const updatedConfig = { ...currentConfig, ...newConfig }

        // 比较配置是否相同
        const isConfigSame = Object.keys(updatedConfig).every(
          key => currentConfig[key as keyof CanvasConfig] === updatedConfig[key as keyof CanvasConfig]
        )

        if (isConfigSame) {
          return state // 配置相同，不更新
        }

        return {
          config: updatedConfig
        }
      }),
      
      setIsReady: (ready) => set({ isReady: ready, error: ready ? null : get().error }),
      
      setError: (error) => set({ error, isReady: error ? false : get().isReady }),
      
      setCurrentImageUrl: (url) => set({ currentImageUrl: url }),
      
      updateSize: (width, height) => set((state) => ({
        config: { ...state.config, width, height }
      })),
      
      reset: () => set(initialState),
      
      // 异步 Actions
      loadImage: async (canvas: FabricCanvas, imageUrl: string, topOffset?: number, bottomOffset?: number) => {
        try {
          set({ error: null })
          
          const { config } = get()
          const actualTopOffset = topOffset ?? config.topOffset
          const actualBottomOffset = bottomOffset ?? config.bottomOffset
          
          const image = await loadImageToCanvas(canvas, imageUrl, actualTopOffset, actualBottomOffset)
          set({ currentImageUrl: imageUrl })
          
          return image
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to load image'
          set({ error: errorMessage })
          throw error
        }
      },
      
      resetImage: (canvas: FabricCanvas) => {
        try {
          set({ error: null })
          resetImagePosition(canvas)
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to reset image'
          set({ error: errorMessage })
        }
      },
      
      updateCanvasSize: (canvas: FabricCanvas, width: number, height: number) => {
        try {
          set({ error: null })
          const { config } = get()
          updateCanvasSize(canvas, width, height, config.topOffset, config.bottomOffset)
          set((state) => ({
            config: { ...state.config, width, height }
          }))
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to update canvas size'
          set({ error: errorMessage })
        }
      },
      
      exportAsDataURL: (canvas: FabricCanvas, format: 'png' | 'jpeg' | 'webp' = 'png', quality: number = 0.9) => {
        try {
          set({ error: null })
          return exportCanvasAsImage(canvas, format, quality)
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to export as DataURL'
          set({ error: errorMessage })
          return null
        }
      },
      
      exportAsBlob: async (canvas: FabricCanvas, format: 'png' | 'jpeg' | 'webp' = 'png', quality: number = 0.9) => {
        try {
          set({ error: null })
          return await exportCanvasAsBlob(canvas, format, quality)
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to export as Blob'
          set({ error: errorMessage })
          return null
        }
      },
    }),
    {
      name: 'canvas-store',
    }
  )
)

// 选择器 Hooks（用于性能优化）
export const useCanvasConfig = () => useCanvasStore(state => state.config)
export const useCanvasIsReady = () => useCanvasStore(state => state.isReady)
export const useCanvasError = () => useCanvasStore(state => state.error)
export const useCurrentImageUrl = () => useCanvasStore(state => state.currentImageUrl)
export const useCanvasSize = () => useCanvasStore(state => ({ 
  width: state.config.width, 
  height: state.config.height 
}))
