/**
 * 图片美化 Zustand Store
 */

import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { processImage, mockProcessImage, downloadProcessedImage } from '@/lib/api/image-beautify'
import { validateImageFile } from '@/lib/utils/validation'
import { createImageFile, cleanupObjectURL, generateFileName, downloadFile, type ImageFile } from '@/lib/utils/file'
import { PROCESSING_STATUS, DEFAULT_PROCESSING_OPTIONS, type ProcessingStatus, type ProcessingType } from '@/lib/constants/image'
import type { ProcessingProgress, ProcessingOptions, ProcessingResponse } from '@/lib/api/types'

// Store 状态接口
interface ImageBeautifyState {
  // 状态数据
  originalImage: ImageFile | null
  processedImage: ImageFile | null
  status: ProcessingStatus
  error: string | null
  progress: ProcessingProgress | null
  
  // 配置
  useMockApi: boolean
  
  // 同步 Actions
  setOriginalImage: (image: ImageFile | null) => void
  setProcessedImage: (image: ImageFile | null) => void
  setStatus: (status: ProcessingStatus) => void
  setError: (error: string | null) => void
  setProgress: (progress: ProcessingProgress | null) => void
  setUseMockApi: (useMock: boolean) => void
  reset: () => void
  
  // 异步 Actions
  uploadImageFile: (file: File) => Promise<void>
  processImageWithType: (type: ProcessingType, options?: Partial<ProcessingOptions>) => Promise<void>
  downloadProcessedImageFile: () => Promise<void>
  
  // 便捷方法（保留一些常用的）
  teethWhitening: (intensity?: number) => Promise<void>
  beautyEnhancement: (intensity?: number) => Promise<void>
}

// 初始状态
const initialState = {
  originalImage: null,
  processedImage: null,
  status: PROCESSING_STATUS.IDLE as ProcessingStatus,
  error: null,
  progress: null,
  useMockApi: true, // 开发阶段默认使用 mock API
}

// 创建 Store
export const useImageBeautifyStore = create<ImageBeautifyState>()(
  devtools(
    (set, get) => ({
      // 初始状态
      ...initialState,
      
      // 同步 Actions
      setOriginalImage: (image) => set({ originalImage: image }),
      setProcessedImage: (image) => set({ processedImage: image }),
      setStatus: (status) => set({ status, error: null }),
      setError: (error) => set({ error, status: PROCESSING_STATUS.ERROR }),
      setProgress: (progress) => set({ progress }),
      setUseMockApi: (useMockApi) => set({ useMockApi }),
      
      reset: () => {
        const { originalImage, processedImage } = get()
        
        // 清理对象 URL
        if (originalImage) {
          cleanupObjectURL(originalImage.url)
        }
        if (processedImage) {
          cleanupObjectURL(processedImage.url)
        }
        
        set(initialState)
      },
      
      // 异步 Actions
      uploadImageFile: async (file: File) => {
        try {
          set({ status: PROCESSING_STATUS.UPLOADING, error: null })
          
          // 验证文件
          const validation = validateImageFile(file)
          if (!validation.valid) {
            throw new Error(validation.error || 'Invalid file')
          }
          
          // 创建图片文件对象
          const imageFile = await createImageFile(file)
          
          // 清理之前的处理结果
          const { processedImage } = get()
          if (processedImage) {
            cleanupObjectURL(processedImage.url)
            set({ processedImage: null })
          }
          
          set({ 
            originalImage: imageFile, 
            status: PROCESSING_STATUS.IDLE 
          })
          
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Upload failed'
          set({ 
            error: errorMessage, 
            status: PROCESSING_STATUS.ERROR 
          })
          throw error
        }
      },
      
      processImageWithType: async (type: ProcessingType, options: Partial<ProcessingOptions> = {}) => {
        const { originalImage, useMockApi } = get()
        
        if (!originalImage) {
          throw new Error('No image to process')
        }
        
        try {
          set({ status: PROCESSING_STATUS.PROCESSING, error: null })
          
          const processingOptions: ProcessingOptions = {
            type,
            intensity: options.intensity || DEFAULT_PROCESSING_OPTIONS.intensity,
            quality: options.quality || DEFAULT_PROCESSING_OPTIONS.quality
          }
          
          // 进度回调
          const handleProgress = (progress: ProcessingProgress) => {
            set({ progress })
          }
          
          let processedData: ProcessingResponse
          if (useMockApi) {
            // 使用模拟 API
            const result = await mockProcessImage(originalImage.url, processingOptions, handleProgress)
            if (!result.success || !result.data) {
              throw new Error(result.error?.message || 'Processing failed')
            }
            processedData = result.data
          } else {
            // 使用真实 API
            processedData = await processImage(originalImage.url, processingOptions, handleProgress)
          }

          // 创建处理后的图片文件对象
          let processedFile: File
          let processedUrl: string

          if (processedData.processedImageBlob) {
            // 如果返回了 Blob
            const fileName = generateFileName(originalImage.file.name, type)
            processedFile = new File([processedData.processedImageBlob], fileName, {
              type: originalImage.file.type
            })
            processedUrl = URL.createObjectURL(processedFile)
          } else if (processedData.processedImageUrl) {
            // 如果返回了 URL，需要下载
            const blob = await downloadProcessedImage(processedData.processedImageUrl)
            const fileName = generateFileName(originalImage.file.name, type)
            processedFile = new File([blob], fileName, {
              type: originalImage.file.type
            })
            processedUrl = URL.createObjectURL(processedFile)
          } else {
            throw new Error('No processed image data received')
          }
          
          const processedImageFile: ImageFile = {
            file: processedFile,
            url: processedUrl,
            width: originalImage.width,
            height: originalImage.height,
            size: processedFile.size,
            format: originalImage.format
          }
          
          // 清理之前的处理结果
          const { processedImage } = get()
          if (processedImage) {
            cleanupObjectURL(processedImage.url)
          }
          
          set({ 
            processedImage: processedImageFile, 
            status: PROCESSING_STATUS.COMPLETED,
            progress: null
          })
          
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Processing failed'
          set({ 
            error: errorMessage, 
            status: PROCESSING_STATUS.ERROR,
            progress: null
          })
          throw error
        }
      },
      
      downloadProcessedImageFile: async () => {
        const { processedImage } = get()
        
        if (!processedImage) {
          throw new Error('No processed image to download')
        }
        
        try {
          set({ error: null })
          downloadFile(processedImage.file, processedImage.file.name)
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Download failed'
          set({ error: errorMessage })
          throw error
        }
      },
      
      // 便捷方法
      teethWhitening: async (intensity: number = DEFAULT_PROCESSING_OPTIONS.intensity) => {
        const { processImageWithType } = get()
        await processImageWithType('teeth-whitening', { intensity })
      },
      
      beautyEnhancement: async (intensity: number = DEFAULT_PROCESSING_OPTIONS.intensity) => {
        const { processImageWithType } = get()
        await processImageWithType('beauty-enhancement', { intensity })
      },
    }),
    {
      name: 'image-beautify-store',
    }
  )
)

// 选择器 Hooks（用于性能优化）
export const useOriginalImage = () => useImageBeautifyStore(state => state.originalImage)
export const useProcessedImage = () => useImageBeautifyStore(state => state.processedImage)
export const useImageBeautifyStatus = () => useImageBeautifyStore(state => state.status)
export const useImageBeautifyError = () => useImageBeautifyStore(state => state.error)
export const useImageBeautifyProgress = () => useImageBeautifyStore(state => state.progress)
export const useHasImage = () => useImageBeautifyStore(state => !!state.originalImage)
export const useHasProcessedImage = () => useImageBeautifyStore(state => !!state.processedImage)
export const useCanProcess = () => useImageBeautifyStore(state =>
  !!state.originalImage && state.status === 'idle'
)


