/**
 * 图片编辑器相关类型定义
 */

// Fabric.js 类型定义
export interface FabricCanvas {
  getWidth(): number
  getHeight(): number
  setDimensions(dimensions: { width: number; height: number }): void
  clear(): void
  add(object: FabricObject): void
  remove(object: FabricObject): void
  getObjects(): FabricObject[]
  getActiveObject(): FabricObject | null
  setActiveObject(object: FabricObject): void
  renderAll(): void
  requestRenderAll(): void
  toDataURL(options?: ExportOptions): string
  toCanvasElement(): HTMLCanvasElement
  setZoom(zoom: number): void
  getZoom(): number
  zoomToPoint(point: FabricPoint, zoom: number): void
  absolutePan(point: FabricPoint): void
  setViewportTransform(transform: number[]): void
  viewportTransform: number[]
  dispose(): void
  on(eventName: string, handler: (event: FabricEvent) => void): void
  off(eventName: string, handler?: (event: FabricEvent) => void): void
  selection: boolean
}

export interface FabricObject {
  type: string
  left?: number
  top?: number
  width?: number
  height?: number
  scaleX?: number
  scaleY?: number
  originX?: string
  originY?: string
  selectable?: boolean
  moveable?: boolean
  hasControls?: boolean
  hasBorders?: boolean
  lockRotation?: boolean
  set(properties: Partial<FabricObject>): void
  get(property: string): unknown
  getScaledWidth(): number
  getScaledHeight(): number
}

export interface FabricImage extends FabricObject {
  type: 'image'
  getOriginalSize?(): { width: number; height: number }
  _originalElement?: HTMLImageElement
}

export interface FabricPoint {
  x: number
  y: number
}

export interface FabricEvent {
  e: Event
  target?: FabricObject
  pointer?: FabricPoint
}

export interface ExportOptions {
  format?: string
  quality?: number
  multiplier?: number
}

// 图片处理类型
export type ProcessingType = 'teeth-whitening' | 'beauty-enhancement'

// 图片处理状态
export type ProcessingStatus = 'idle' | 'uploading' | 'processing' | 'completed' | 'error'

// 支持的图片格式
export type SupportedImageFormat = 'image/jpeg' | 'image/png' | 'image/webp'

// 图片文件信息
export interface ImageFile {
  file: File
  url: string
  width: number
  height: number
  size: number
  format: SupportedImageFormat
}

// 图片处理选项
export interface ProcessingOptions {
  type: ProcessingType
  intensity?: number // 处理强度 0-100
  quality?: number   // 输出质量 0-100
}

// API 响应类型
export interface ProcessingApiResponse {
  success: boolean
  data?: {
    processedImageUrl: string
    processedImageBlob?: Blob
  }
  error?: {
    code: string
    message: string
  }
}

// 画布配置
export interface CanvasConfig {
  width: number
  height: number
  backgroundColor: string
}

// 图片美化器状态
export interface ImageBeautifyState {
  originalImage: ImageFile | null
  processedImage: ImageFile | null
  canvas: FabricCanvas | null
  status: ProcessingStatus
  error: string | null
  isCanvasReady: boolean
}

// 图片美化器操作
export interface ImageBeautifyActions {
  uploadImage: (file: File) => Promise<void>
  processImage: (options: ProcessingOptions) => Promise<void>
  downloadImage: () => void
  resetEditor: () => void
  initializeCanvas: (canvasElement: HTMLCanvasElement) => void
  updateCanvasSize: (width: number, height: number) => void
}

// 文件上传组件 Props
export interface FileUploadProps {
  onFileSelect: (file: File) => void
  accept?: string
  maxSize?: number // MB
  disabled?: boolean
  className?: string
  instructionText?: string
  buttonText?: string
}

// 处理按钮组件 Props
export interface ProcessingButtonProps {
  type: ProcessingType
  onClick: () => void
  loading?: boolean
  disabled?: boolean
  className?: string
}

// 下载按钮组件 Props
export interface DownloadButtonProps {
  onDownload: () => void
  disabled?: boolean
  fileName?: string
  className?: string
}

// 图片画布组件 Props
export interface ImageCanvasProps {
  width: number
  height: number
  imageUrl?: string
  onCanvasReady?: (canvas: FabricCanvas) => void
  onImageLoaded?: (image: FabricImage) => void
  onError?: (error: Error) => void
  className?: string
}

// 控制面板组件 Props
export interface ControlPanelProps {
  onTeethWhitening: () => void // 点击“牙齿美白”按钮时回调（仅用于切换显示/隐藏）
  onSelectTeethIntensity: (value: number) => void
  showTeethOptions: boolean
  selectedTeethIntensity?: number | null // 当前选中的牙齿亮度值
  onBeautyEnhancement: () => void
  onDownload: () => void
  processingStatus: ProcessingStatus
  hasImage: boolean
  hasProcessedImage: boolean
  onToggleCompare?: () => void
  isCompareMode?: boolean
  showCompareButton?: boolean
  className?: string
}

// 图片美化器主组件 Props
export interface ImageBeautifyProps {
  className?: string
}

// 错误类型
export interface ImageBeautifyError {
  type: 'upload' | 'processing' | 'download' | 'canvas'
  message: string
  details?: unknown
}

// 画布事件类型
export interface CanvasEvents {
  onImageLoaded?: (image: FabricImage) => void
  onImageMoved?: (image: FabricImage) => void
  onImageScaled?: (image: FabricImage) => void
  onError?: (error: Error) => void
}

// 图片处理进度
export interface ProcessingProgress {
  stage: 'uploading' | 'analyzing' | 'processing' | 'optimizing' | 'completed'
  progress: number // 0-100
  message?: string
}

// 图片元数据
export interface ImageMetadata {
  width: number
  height: number
  aspectRatio: number
  fileSize: number
  format: string
  colorSpace?: string
  hasAlpha?: boolean
}

// 画布操作历史
export interface CanvasHistory {
  states: string[]
  currentIndex: number
  maxStates: number
}

// 导出配置
export interface ExportConfig {
  format: 'png' | 'jpeg' | 'webp'
  quality: number
  width?: number
  height?: number
  maintainAspectRatio: boolean
}
