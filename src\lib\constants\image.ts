/**
 * 图片相关常量
 */

// 支持的图片格式
export const SUPPORTED_IMAGE_FORMATS = [
  'image/jpeg',
  'image/png',
  'image/webp'
] as const

export type SupportedImageFormat = typeof SUPPORTED_IMAGE_FORMATS[number]

// 文件大小限制 (10MB)
export const MAX_FILE_SIZE = 10 * 1024 * 1024

// 图片处理类型
export const PROCESSING_TYPES = {
  TEETH_WHITENING: 'teeth-whitening',
  BEAUTY_ENHANCEMENT: 'beauty-enhancement'
} as const

export type ProcessingType = typeof PROCESSING_TYPES[keyof typeof PROCESSING_TYPES]

// 处理状态
export const PROCESSING_STATUS = {
  IDLE: 'idle',
  UPLOADING: 'uploading',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  ERROR: 'error'
} as const

export type ProcessingStatus = typeof PROCESSING_STATUS[keyof typeof PROCESSING_STATUS]

// 默认处理参数
export const DEFAULT_PROCESSING_OPTIONS = {
  intensity: 50,
  quality: 90
} as const

// 错误类型
export const ERROR_TYPES = {
  UPLOAD: 'upload',
  PROCESSING: 'processing',
  DOWNLOAD: 'download',
  VALIDATION: 'validation'
} as const

export type ErrorType = typeof ERROR_TYPES[keyof typeof ERROR_TYPES]
