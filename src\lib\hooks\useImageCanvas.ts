/**
 * 图片画布 Hook - 使用 Zustand Store
 */

'use client'

import { useRef, useCallback, useEffect, useMemo } from 'react'
import {
  initializeCanvas,
  disposeCanvas,
  enableMouseWheelZoom,
  enableDoubleClickReset,
  enableCanvasPanning,
  enableTouchpadGestures as enableTouchpadGesturesFunction
} from '@/lib/utils/canvas'
import { useCanvasStore, useCanvasIsReady, useCanvasError, type CanvasConfig } from '@/lib/stores/canvas'
import type {
  FabricCanvas,
  FabricImage
} from '@/types/image-beautify'

type UseImageCanvasOptions = Partial<CanvasConfig>

interface UseImageCanvasReturn {
  canvasRef: React.RefObject<HTMLCanvasElement>
  canvas: FabricCanvas | null
  isReady: boolean
  error: string | null
  loadImage: (imageUrl: string, topOffset?: number, bottomOffset?: number) => Promise<FabricImage>
  resetImage: () => void
  updateSize: (width: number, height: number) => void
  exportAsDataURL: (format?: 'png' | 'jpeg' | 'webp', quality?: number) => string | null
  exportAsBlob: (format?: 'png' | 'jpeg' | 'webp', quality?: number) => Promise<Blob | null>
}

export function useImageCanvas(options: UseImageCanvasOptions = {}): UseImageCanvasReturn {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const canvasInstanceRef = useRef<FabricCanvas | null>(null)
  const configInitialized = useRef(false)

  // 使用 Zustand store
  const isReady = useCanvasIsReady()
  const error = useCanvasError()
  const {
    setConfig,
    setIsReady,
    setError,
    loadImage: storeLoadImage,
    resetImage: storeResetImage,
    updateCanvasSize: storeUpdateCanvasSize,
    exportAsDataURL: storeExportAsDataURL,
    exportAsBlob: storeExportAsBlob
  } = useCanvasStore()

  // 设置默认值
  const config: CanvasConfig = useMemo(() => ({
    width: 800,
    height: 600,
    backgroundColor: '#000000',
    enableZoom: false,
    enablePanning: false,
    enableReset: false,
    enableTouchpadGestures: false,
    topOffset: 0,
    bottomOffset: 0,
    ...options
  }), [options])

  // 只在第一次时设置配置，避免循环更新
  if (!configInitialized.current) {
    setConfig(config)
    configInitialized.current = true
  }

  // 初始化画布
  const initCanvas = useCallback(() => {
    if (!canvasRef.current || canvasInstanceRef.current) {
      return
    }

    const canvasElement = canvasRef.current
    const canvasWithFabric = canvasElement as HTMLCanvasElement & {
      __fabric?: unknown
      fabric?: unknown
    }

    // 检查多个可能的 Fabric.js 标记
    if (canvasWithFabric.__fabric || canvasWithFabric.fabric) {
      setIsReady(true)
      return
    }

    // 检查是否已经有 Fabric.js 相关的类名
    if (canvasElement.classList.contains('canvas-container') ||
        canvasElement.classList.contains('lower-canvas') ||
        canvasElement.parentElement?.classList.contains('canvas-container')) {
      setIsReady(true)
      return
    }

    try {
      const canvas = initializeCanvas(canvasElement, config)
      canvasInstanceRef.current = canvas

      // 根据配置启用交互功能
      if (config.enableZoom) {
        enableMouseWheelZoom(canvas)
      }

      if (config.enablePanning) {
        enableCanvasPanning(canvas)
      }

      if (config.enableReset) {
        enableDoubleClickReset(canvas)
      }

      if (config.enableTouchpadGestures) {
        enableTouchpadGesturesFunction(canvas)
      }

      setIsReady(true)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to initialize canvas'
      setError(errorMessage)
    }
  }, [config, setIsReady, setError])

  // 加载图片
  const loadImage = useCallback(async (imageUrl: string, topOffset?: number, bottomOffset?: number): Promise<FabricImage> => {
    if (!canvasInstanceRef.current) {
      throw new Error('Canvas not initialized')
    }

    return storeLoadImage(canvasInstanceRef.current, imageUrl, topOffset, bottomOffset)
  }, [storeLoadImage])

  // 重置图片位置
  const resetImage = useCallback(() => {
    if (!canvasInstanceRef.current) return
    storeResetImage(canvasInstanceRef.current)
  }, [storeResetImage])

  // 更新画布尺寸
  const updateSize = useCallback((newWidth: number, newHeight: number) => {
    if (!canvasInstanceRef.current) return
    storeUpdateCanvasSize(canvasInstanceRef.current, newWidth, newHeight)
  }, [storeUpdateCanvasSize])

  // 导出为 DataURL
  const exportAsDataURL = useCallback((format: 'png' | 'jpeg' | 'webp' = 'png', quality: number = 0.9): string | null => {
    if (!canvasInstanceRef.current) return null
    return storeExportAsDataURL(canvasInstanceRef.current, format, quality)
  }, [storeExportAsDataURL])

  // 导出为 Blob
  const exportAsBlob = useCallback(async (format: 'png' | 'jpeg' | 'webp' = 'png', quality: number = 0.9): Promise<Blob | null> => {
    if (!canvasInstanceRef.current) return null
    return storeExportAsBlob(canvasInstanceRef.current, format, quality)
  }, [storeExportAsBlob])

  // 初始化画布
  useEffect(() => {
    initCanvas()
  }, [initCanvas])

  // 清理
  useEffect(() => {
    return () => {
      if (canvasInstanceRef.current) {
        disposeCanvas(canvasInstanceRef.current)
        canvasInstanceRef.current = null
        setIsReady(false)
      }
    }
  }, [setIsReady])

  return {
    canvasRef: canvasRef as React.RefObject<HTMLCanvasElement>,
    canvas: canvasInstanceRef.current,
    isReady,
    error,
    loadImage,
    resetImage,
    updateSize,
    exportAsDataURL,
    exportAsBlob
  }
}
