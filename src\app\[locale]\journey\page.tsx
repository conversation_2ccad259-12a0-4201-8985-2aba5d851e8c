/**
 * 尊享之旅
 */
import { getPage } from '@/lib/cms/page'
import Image from 'next/image'
import type { StrapiHero } from '@/types/strapi'

// 区块组件
const BlockRenderer = ({ block }: { block: any }) => {
  const { __component, title, description, media } = block

  // 根据不同的组件类型渲染不同的布局
  switch (__component) {
    case 'shared.block':
      // 第一个模块：品牌介绍模块 - 上中下结构
      if (
        title === 'Lumii：源自女神的微光之美' ||
        title === 'Lumii: The Beauty of Light Originating from the Goddess'
      ) {
        return (
          <section className="px-4 py-10 lg:px-0 lg:py-20">
            <div className="flex flex-col">
              {/* 上部：图片 */}
              <div className="mb-5 lg:mb-12">
                {media && (
                  <div className="relative">
                    <Image
                      src={media.url}
                      alt={title || ''}
                      width={1200}
                      height={0}
                      className="h-auto w-full object-cover"
                    />
                  </div>
                )}
              </div>

              {/* 中部：标题 */}
              <div className="text-center">
                <h2 className="mb-2 text-[20px] font-[500] lg:text-[22px]">{title}</h2>
              </div>

              {/* 下部：描述文字 */}

              <p className="line-clamp-responsive text-justify text-[14px] leading-[24px] text-[#444444]">
                {description}
              </p>
            </div>
          </section>
        )
      }
      if (title == 'Fernando Autran') {
        return (
          <section className="px-4 py-10 lg:px-0 lg:py-20">
            <div className="flex flex-col lg:flex-row lg:items-start lg:gap-20">
              {/* 移动端：标题 */}
              <div className="lg:hidden">
                <h2 className="mb-3 text-[20px]">{title}</h2>
              </div>

              {/* 左侧图片 */}
              <div className="order-2 lg:order-1">
                {media && (
                  <Image
                    src={media.url}
                    alt={title || ''}
                    width={500}
                    height={320}
                    className="h-auto w-full object-cover lg:h-auto lg:w-full"
                  />
                )}
              </div>

              {/* 右侧内容 */}
              <div className="order-1 flex h-full flex-col lg:order-2 lg:flex-1 lg:justify-between">
                <div>
                  {/* PC端：标题 */}
                  <h2 className="mb-8 hidden text-4xl font-[30px] lg:block">{title}</h2>

                  {/* 描述文字 */}
                  <div className="line-clamp-responsive mb-7.5 text-sm leading-[28px] text-[#444444] lg:mb-0">
                    {description}
                  </div>
                </div>
              </div>
            </div>
          </section>
        )
      }

    default:
      return null
  }
}

export default async function Home({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params
  const pageData = await getPage(locale, 'journey')
  
  return (
    <div className="pt-16 lg:pt-24">
      {/* Hero Banner */}
      <div className="relative">
        {pageData?.heroes.map((item: StrapiHero) => (
          <div key={item.id}>
            <Image
              key={item.id}
              src={item?.media?.url || ''}
              alt={item.title || ''}
              width={1200}
              height={800}
              sizes="100vw"
              className="h-auto w-full object-cover"
            />
            <div className="text-[#fff] width-full absolute left-7.5 top-10 lg:top-[20%] lg:left-[12%]   z-10">
              <div className="w-full lg:w-[60%]">
                <h1 className="text-[20px] font-[500] lg:mb-8 lg:text-[36px]">{item.title}</h1>
                <span className="hidden text-sm lg:block"> {item.description}</span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Blocks Content */}
      <div className="bg-[#FAF6F2]">
        <div className="lg:mx-auto lg:w-[76%]">
          {pageData?.blocks.map((block: any, index: number) => (
            <div key={block.id || index}>
              <BlockRenderer block={block} />
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
