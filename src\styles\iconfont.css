/* Iconfont 样式 - 使用 CSS 变量 */
.iconfont {
  font-family: var(--font-iconfont) !important;
  font-size: 16px;
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  display: inline-block;
  vertical-align: middle;
}

/* 图标类名 */
.icon-yijianmeiyan1:before {
  content: "\e64d";
}

.icon-douyin:before {
  content: "\e648";
}

.icon-xiala2:before {
  content: "\e649";
}

.icon-xiaochengxu:before {
  content: "\e64a";
}

.icon-wancheng:before {
  content: "\e64b";
}

.icon-xiala:before {
  content: "\e64c";
}

.icon-zanting:before {
  content: "\e63a";
}

.icon-dianhua:before {
  content: "\e63b";
}

.icon-yachimeibai:before {
  content: "\e639";
}

.icon-weixin:before {
  content: "\e63c";
}

.icon-weizhi:before {
  content: "\e63d";
}

.icon-xiaohongshu:before {
  content: "\e638";
}

.icon-shijian:before {
  content: "\e63e";
}

.icon-shangchuanzhaopian:before {
  content: "\e640";
}

.icon-bofang:before {
  content: "\e641";
}

.icon-shouqi:before {
  content: "\e642";
}

.icon-guanbi:before {
  content: "\e643";
}

.icon-caidan:before {
  content: "\e644";
}

.icon-duibi:before {
  content: "\e645";
}

.icon-weibo:before {
  content: "\e646";
}

.icon-qiehuan:before {
  content: "\e647";
}

/* 常用尺寸类 */
.iconfont-xs { font-size: 12px; }
.iconfont-sm { font-size: 14px; }
.iconfont-base { font-size: 16px; }
.iconfont-lg { font-size: 18px; }
.iconfont-xl { font-size: 20px; }
.iconfont-2xl { font-size: 24px; }
.iconfont-3xl { font-size: 30px; }
.iconfont-4xl { font-size: 36px; }
