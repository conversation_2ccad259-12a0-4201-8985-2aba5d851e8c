'use client'

import { motion } from 'motion/react'
import Link from 'next/link'
import { useState, useCallback, useEffect } from 'react'
import Image from 'next/image'
import LanguageSwitcher from '@/components/ui/LanguageSwitcher'
import { useTranslation } from '@/lib/i18n/client'
import { useHeaderHeight } from '@/lib/contexts/HeaderHeightContext'
import type { IHeader, ISite } from '@/types/strapi'

import Logo from '/public/images/logo.png'

interface HeaderProps {
  data?: IHeader | null
  siteData?: ISite | null
  ref?: React.Ref<HTMLElement>
}

export default function Header({ data, siteData, ref }: HeaderProps) {
  const { locale } = useTranslation()
  const { headerRef } = useHeaderHeight()
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [menus] = useState(data?.menus || [])
  const [site] = useState(siteData || null)
  const [activeMenu, setActiveMenu] = useState<number | null>(null)

  // Combine refs
  const combinedRef = useCallback((node: HTMLElement | null) => {
    headerRef(node)
    if (typeof ref === 'function') {
      ref(node)
    } else if (ref) {
      ref.current = node
    }
  }, [headerRef, ref])
  // PC端滚动隐藏/显示状态
  const [isHeaderVisible, setIsHeaderVisible] = useState(true)
  const [lastScrollY, setLastScrollY] = useState(0)

  // 监听滚动事件（仅PC端）
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY

      // 只在PC端应用滚动隐藏逻辑
      if (window.innerWidth >= 1024) { // lg breakpoint
        if (currentScrollY > lastScrollY && currentScrollY > 100) {
          // 向下滚动且超过100px时隐藏
          setIsHeaderVisible(false)
        } else if (currentScrollY < lastScrollY) {
          // 向上滚动时显示
          setIsHeaderVisible(true)
        }
      } else {
        // 移动端始终显示
        setIsHeaderVisible(true)
      }

      setLastScrollY(currentScrollY)
    }

    // 监听窗口大小变化，确保移动端始终显示Header
    const handleResize = () => {
      if (window.innerWidth < 1024) {
        setIsHeaderVisible(true)
      }
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('scroll', handleScroll)
      window.removeEventListener('resize', handleResize)
    }
  }, [lastScrollY])

  return (
    <motion.header
      ref={combinedRef}
      initial={{ y: -100 }}
      animate={{
        y: isHeaderVisible ? 0 : -100
      }}
      transition={{
        duration: 0.3,
        ease: "easeInOut"
      }}
      className="fixed top-0 right-0 left-0 z-50 bg-black"
    >
      {/* Desktop Layout */}
      <div className="hidden lg:block">
        <div className="w-[76%] mx-auto ">
          {/* Logo Section */}
          <div className=" flex items-center justify-center py-4 relative">
            <motion.div whileHover={{ scale: 1 }} className="flex-shrink-0">
              <Link href={`/${locale}`} className="block">
                <Image
                  src={site?.logo?.url || Logo}
                  alt="Lumii Logo"
                  width={120}
                  height={40}
                  className="h-8 w-auto"
                />
              </Link>
            </motion.div>
            {/* Language Switcher */}
            <div className="absolute top-4 right-[0px]">
              <LanguageSwitcher />
            </div>
          </div>

          {/* Navigation Section */}
          <div className="flex items-center justify-between">
            <nav className="flex flex-1 items-center justify-center pb-2">
              <div className="flex space-x-8">
                {menus.map((item, index) => {
                  const isActive = activeMenu === item.id
                  return (
                    <motion.div
                      key={item.id}
                      initial={{ opacity: 0, y: -20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: index * 0.1 }}
                    >
                      <Link
                        href={`/${locale}/${item.url}`}
                        onClick={() => setActiveMenu(item.id)}
                        className={`relative px-3 py-2 text-[14px] whitespace-nowrap text-white transition-all duration-200 hover:font-[600] ${
                          isActive
                            ? 'font-semibold after:absolute after:bottom-0 after:left-1/2 after:h-0.5 after:w-8 after:-translate-x-1/2 after:transform after:bg-white after:content-[""]'
                            : 'font-light hover:font-semibold hover:after:absolute hover:after:bottom-0 hover:after:left-1/2 hover:after:h-0.5 hover:after:w-8 hover:after:-translate-x-1/2 hover:after:transform hover:after:bg-white hover:after:content-[""]'
                        }`}
                      >
                        {item.label}
                      </Link>
                    </motion.div>
                  )
                })}
              </div>
            </nav>
          </div>
        </div>
      </div>

      {/* Mobile Layout */}
      <div className="lg:hidden">
        <div className="mx-auto max-w-full px-4 sm:px-6">
          <div className="flex h-16 items-center justify-between">
            {/* Logo */}
            <motion.div whileHover={{ scale: 1.05 }} className="flex-shrink-0">
              <Link href={`/${locale}`} className="block">
                <Image src={Logo} alt="Lumii Logo" width={120} height={40} className="h-8 w-auto" />
              </Link>
            </motion.div>

            {/* Mobile menu button */}
            <div>
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="p-2 text-white hover:text-gray-300 focus:text-gray-300 focus:outline-none"
                aria-label="Toggle menu"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  {isMenuOpen ? (
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  ) : (
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 6h16M4 12h16M4 18h16"
                    />
                  )}
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="space-y-1 border-t border-gray-800 bg-black px-2 pt-2 pb-4">
              {menus.map((item) => (
                <Link
                  key={item.id}
                  href={`/${locale}`}
                  className="block border-b border-gray-800 px-3 py-3 text-base font-medium text-white last:border-b-0 hover:text-gray-300"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.label}
                </Link>
              ))}
              <div className="mt-2  px-3 py-3">
                <LanguageSwitcher />
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </motion.header>
  )
}
