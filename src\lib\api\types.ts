/**
 * API 相关类型定义
 */

// 基础 API 响应类型
export interface ApiResponse<T = unknown> {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
    details?: unknown
  }
  meta?: {
    timestamp: number
    requestId?: string
  }
}

// API 错误类型
export interface ApiError {
  code: string
  message: string
  status?: number
  details?: unknown
}

// 请求配置类型
export interface RequestConfig extends RequestInit {
  timeout?: number
  retries?: number
  retryDelay?: number
  baseURL?: string
}

// 图片处理相关类型
export interface ProcessingOptions {
  type: 'teeth-whitening' | 'beauty-enhancement'
  intensity: number
  quality: number
}

export interface ProcessingProgress {
  stage: 'analyzing' | 'processing' | 'optimizing' | 'completed'
  progress: number
  message: string
}

export interface ProcessingResult {
  processedImageUrl?: string
  processedImageBlob?: Blob
}

// 上传响应类型
export interface UploadResponse {
  imageUrl: string
  imageId: string
}

// 处理响应类型
export interface ProcessingResponse {
  processedImageUrl: string
  processedImageBlob?: Blob
}
