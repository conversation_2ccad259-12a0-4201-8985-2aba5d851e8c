/**
 * Fabric.js 工具函数
 */

import * as fabric from 'fabric'
import type {
  FabricCanvas,
  FabricImage,
  FabricPoint,
  CanvasConfig,
  ImageMetadata,
  ExportOptions,
  FabricEvent
} from '@/types/image-beautify'

/**
 * 初始化 Fabric.js 画布
 */
export function initializeCanvas(
  canvasElement: HTMLCanvasElement,
  config: CanvasConfig & {
    enableInteraction?: boolean
  }
): FabricCanvas {

  // 多重检查 Canvas 元素是否已经被 Fabric.js 初始化
  type CanvasWithFabric = HTMLCanvasElement & {
    __fabric?: unknown
    fabric?: unknown
  }

  const canvasWithFabric = canvasElement as CanvasWithFabric

  // 检查多个可能的 Fabric.js 标记
  if (canvasWithFabric.__fabric || canvasWithFabric.fabric) {
    throw new Error('Canvas element has already been initialized by Fabric.js')
  }

  // 检查是否已经有 Fabric.js 相关的类名
  if (canvasElement.classList.contains('canvas-container') ||
      canvasElement.classList.contains('lower-canvas') ||
      canvasElement.parentElement?.classList.contains('canvas-container')) {
    throw new Error('Canvas element appears to be already initialized by Fabric.js')
  }

  const enableInteraction = config.enableInteraction ?? false

  const canvas = new fabric.Canvas(canvasElement, {
    width: config.width,
    height: config.height,
    backgroundColor: config.backgroundColor,
    selection: false,
    preserveObjectStacking: true,
    // 根据配置决定是否启用交互
    allowTouchScrolling: enableInteraction,
    moveCursor: 'default',
    defaultCursor: 'default',
    hoverCursor: 'default',
    // 根据配置决定是否启用事件
    interactive: enableInteraction,
    evented: enableInteraction,
  }) as FabricCanvas

  // 设置画布样式
  canvas.setDimensions({
    width: config.width,
    height: config.height,
  })

  // 手动设置 upper-canvas 的光标样式
  const canvasWithElements = canvas as FabricCanvas & {
    upperCanvasEl?: HTMLCanvasElement
    lowerCanvasEl?: HTMLCanvasElement
  }

  const upperCanvas = canvasWithElements.upperCanvasEl
  if (upperCanvas) {
    upperCanvas.style.cursor = 'default'
    // 根据交互配置设置触摸行为
    upperCanvas.style.touchAction = enableInteraction ? 'none' : 'auto'
  }

  // 设置 lower-canvas 的光标样式
  const lowerCanvas = canvasWithElements.lowerCanvasEl
  if (lowerCanvas) {
    lowerCanvas.style.cursor = 'default'
  }

  return canvas
}

/**
 * 加载图片到画布
 */
export function loadImageToCanvas(
  canvas: FabricCanvas,
  imageUrl: string,
  topOffset: number = 0,
  bottomOffset: number = 0
): Promise<FabricImage> {
  return new Promise((resolve, reject) => {
    fabric.FabricImage.fromURL(imageUrl, {
      crossOrigin: 'anonymous',
    }).then((img) => {
      if (!img) {
        reject(new Error('Failed to load image'))
        return
      }

      // 清除画布上的所有对象
      canvas.clear()

      // 计算图片缩放比例以适应可用区域
      const canvasWidth = canvas.getWidth()
      const canvasHeight = canvas.getHeight()

      // 计算实际可用区域
      const availableWidth = canvasWidth
      const availableHeight = canvasHeight - topOffset - bottomOffset

      // 获取图片原始尺寸
      const imgWidth = (img as FabricImage).width || 1
      const imgHeight = (img as FabricImage).height || 1

      // 计算缩放比例，实现 contain 效果（贴边）
      const scaleX = availableWidth / imgWidth
      const scaleY = availableHeight / imgHeight
      const scale = Math.min(scaleX, scaleY) // 移除 0.8 系数，实现贴边效果

      // 计算图片在可用区域内的居中位置
      const centerX = canvasWidth / 2
      const centerY = topOffset + availableHeight / 2

      // 设置图片属性
      img.set({
        left: centerX,
        top: centerY,
        originX: 'center',
        originY: 'center',
        scaleX: scale,
        scaleY: scale,
        selectable: false,
        hasControls: false,
        hasBorders: false,
        lockRotation: true,
      })

      // 添加到画布
      // 使用类型断言来解决 Fabric.js 类型兼容性问题
      const fabricCanvas = canvas as unknown as fabric.Canvas
      fabricCanvas.add(img)
      canvas.renderAll()

      resolve(img as FabricImage)
    }).catch(reject)
  })
}

/**
 * 更新画布尺寸，并按可用区域(top/bottom offset)重新适配图片
 */
export function updateCanvasSize(
  canvas: FabricCanvas,
  width: number,
  height: number,
  topOffset: number = 0,
  bottomOffset: number = 0
): void {
  // 更新画布尺寸
  canvas.setDimensions({ width, height })

  // 查找画布中的图片对象（不依赖 activeObject）
  const imgObj = canvas.getObjects().find((o) => o.type === 'image') as FabricImage | undefined

  if (imgObj) {
    // 计算可用区域
    const availableWidth = width
    const availableHeight = Math.max(0, height - topOffset - bottomOffset)

    const imgWidth = imgObj.width || 1
    const imgHeight = imgObj.height || 1

    // contain 缩放，贴近可视区域
    const scaleX = availableWidth / imgWidth
    const scaleY = availableHeight / imgHeight
    const scale = Math.min(scaleX, scaleY)

    // 区域居中
    const centerX = width / 2
    const centerY = topOffset + availableHeight / 2

    imgObj.set({
      left: centerX,
      top: centerY,
      originX: 'center',
      originY: 'center',
      scaleX: scale,
      scaleY: scale,
      selectable: false,
      hasControls: false,
      hasBorders: false,
      lockRotation: true,
    })
  }

  canvas.renderAll()
}

/**
 * 重置图片位置和大小
 */
export function resetImagePosition(canvas: FabricCanvas): void {
  const activeObject = canvas.getActiveObject()
  if (!activeObject || activeObject.type !== 'image') return

  const img = activeObject as FabricImage
  const canvasWidth = canvas.getWidth()
  const canvasHeight = canvas.getHeight()
  const imgWidth = img.width || 1
  const imgHeight = img.height || 1

  const scaleX = canvasWidth / imgWidth
  const scaleY = canvasHeight / imgHeight
  const scale = Math.min(scaleX, scaleY, 1)

  img.set({
    left: canvasWidth / 2,
    top: canvasHeight / 2,
    scaleX: scale,
    scaleY: scale,
  })

  canvas.renderAll()
}

/**
 * 获取图片元数据
 */
export function getImageMetadata(img: FabricImage): ImageMetadata {
  const width = img.width || 0
  const height = img.height || 0

  return {
    width,
    height,
    aspectRatio: width / height,
    fileSize: 0, // Fabric.js 无法获取文件大小
    format: 'unknown',
  }
}

/**
 * 导出画布为图片
 */
export function exportCanvasAsImage(
  canvas: FabricCanvas,
  format: 'png' | 'jpeg' | 'webp' = 'png',
  quality: number = 0.9
): string {
  const options: ExportOptions = {
    format: `image/${format}`,
    quality,
    multiplier: 1,
  }
  return canvas.toDataURL(options)
}

/**
 * 导出画布为 Blob
 */
export function exportCanvasAsBlob(
  canvas: FabricCanvas,
  format: 'png' | 'jpeg' | 'webp' = 'png',
  quality: number = 0.9
): Promise<Blob | null> {
  return new Promise((resolve) => {
    canvas.toCanvasElement().toBlob(
      (blob: Blob | null) => resolve(blob),
      `image/${format}`,
      quality
    )
  })
}

/**
 * 启用触摸板双指手势（缩放和平移）
 */
export function enableTouchpadGestures(canvas: FabricCanvas): void {
  canvas.on('mouse:wheel', (opt: FabricEvent) => {
    const e = opt.e as WheelEvent

    // 检测是否是触摸板双指手势
    // ctrlKey 通常表示触摸板的缩放手势
    // shiftKey + wheel 通常表示水平滚动
    // 普通的 wheel 事件表示垂直滚动

    if (e.ctrlKey) {
      // 双指缩放手势
      const delta = e.deltaY
      let zoom = canvas.getZoom()

      // 计算新的缩放级别
      zoom *= 0.999 ** delta

      // 限制缩放范围
      zoom = Math.max(0.1, Math.min(5, zoom))

      // 以鼠标位置为中心缩放
      const point: FabricPoint = { x: e.offsetX, y: e.offsetY }
      canvas.zoomToPoint(point, zoom)

      e.preventDefault()
      e.stopPropagation()
    } else if (e.shiftKey || Math.abs(e.deltaX) > Math.abs(e.deltaY)) {
      // 双指水平平移手势
      const vpt = canvas.viewportTransform
      if (vpt) {
        vpt[4] -= e.deltaX * 0.5 // 水平平移
        vpt[5] -= e.deltaY * 0.5 // 垂直平移（如果有的话）
        canvas.setViewportTransform(vpt)
        canvas.requestRenderAll()
      }

      e.preventDefault()
      e.stopPropagation()
    } else {
      // 双指垂直平移手势
      const vpt = canvas.viewportTransform
      if (vpt) {
        vpt[4] -= e.deltaX * 0.5 // 水平平移
        vpt[5] -= e.deltaY * 0.5 // 垂直平移
        canvas.setViewportTransform(vpt)
        canvas.requestRenderAll()
      }

      e.preventDefault()
      e.stopPropagation()
    }
  })
}

/**
 * 添加鼠标滚轮缩放功能（传统方式）
 */
export function enableMouseWheelZoom(canvas: FabricCanvas): void {
  canvas.on('mouse:wheel', (opt: FabricEvent) => {
    const event = opt.e as WheelEvent
    const delta = event.deltaY
    let zoom = canvas.getZoom()
    zoom *= 0.999 ** delta

    // 限制缩放范围
    zoom = Math.max(0.1, Math.min(5, zoom))

    const point: FabricPoint = { x: event.offsetX, y: event.offsetY }
    canvas.zoomToPoint(point, zoom)

    event.preventDefault()
    event.stopPropagation()
  })
}

/**
 * 添加双击重置功能
 */
export function enableDoubleClickReset(canvas: FabricCanvas): void {
  canvas.on('mouse:dblclick', () => {
    resetImagePosition(canvas)
    canvas.setZoom(1)
    canvas.absolutePan({ x: 0, y: 0 })
  })
}

/**
 * 设置画布拖拽模式
 */
export function enableCanvasPanning(canvas: FabricCanvas): void {
  let isDragging = false
  let lastPosX = 0
  let lastPosY = 0

  canvas.on('mouse:down', (opt: FabricEvent) => {
    const evt = opt.e as MouseEvent
    if (evt.altKey || evt.ctrlKey) {
      isDragging = true
      canvas.selection = false
      lastPosX = evt.clientX
      lastPosY = evt.clientY
    }
  })

  canvas.on('mouse:move', (opt: FabricEvent) => {
    if (isDragging) {
      const evt = opt.e as MouseEvent
      const vpt = canvas.viewportTransform
      if (vpt) {
        vpt[4] += evt.clientX - lastPosX
        vpt[5] += evt.clientY - lastPosY
        canvas.requestRenderAll()
        lastPosX = evt.clientX
        lastPosY = evt.clientY
      }
    }
  })

  canvas.on('mouse:up', () => {
    canvas.setViewportTransform(canvas.viewportTransform)
    isDragging = false
    canvas.selection = true
  })
}

/**
 * 清理画布事件监听器
 */
export function cleanupCanvasEvents(canvas: FabricCanvas): void {
  canvas.off('mouse:wheel')
  canvas.off('mouse:dblclick')
  canvas.off('mouse:down')
  canvas.off('mouse:move')
  canvas.off('mouse:up')
}

/**
 * 销毁画布
 */
export function disposeCanvas(canvas: FabricCanvas): void {
  cleanupCanvasEvents(canvas)
  canvas.clear()
  canvas.dispose()
}
